# ArchitekAI Static Deployment Guide

This guide covers deploying the ArchitekAI React frontend as a static website on various platforms.

## 🚀 Quick Start

The React frontend is fully configured for static deployment with the following features:
- ✅ Single Page Application (SPA) routing
- ✅ Optimized build configuration
- ✅ Static asset optimization
- ✅ Progressive Web App (PWA) ready
- ✅ Security headers configured
- ✅ SEO optimized

## 📋 Prerequisites

1. **Build the application:**
   ```bash
   cd src/web/frontend
   npm install
   npm run build
   ```

2. **Verify build output:**
   - Built files will be in `src/web/frontend/dist/`
   - Check that `index.html` and `assets/` directory exist

## 🌐 Deployment Options

### 1. Netlify (Recommended)

**Option A: Drag & Drop**
1. Build the app locally: `npm run build`
2. Go to [Netlify](https://netlify.com)
3. Drag the `dist/` folder to the deploy area

**Option B: Git Integration**
1. Connect your GitHub repository
2. Set build settings:
   - **Build command:** `cd src/web/frontend && npm run build`
   - **Publish directory:** `src/web/frontend/dist`
   - **Node version:** 18

**Configuration files included:**
- `public/_redirects` - SPA routing & API redirects
- `public/netlify.toml` - Build settings & headers

### 2. Vercel

**Option A: CLI Deployment**
```bash
cd src/web/frontend
npx vercel --prod
```

**Option B: Git Integration**
1. Connect repository at [vercel.com](https://vercel.com)
2. Set build settings:
   - **Framework:** Vite
   - **Build command:** `npm run build`
   - **Output directory:** `dist`
   - **Root directory:** `src/web/frontend`

**Configuration:** `public/vercel.json` included

### 3. GitHub Pages

1. **Enable GitHub Pages** in repository settings
2. **Create deployment workflow** (`.github/workflows/deploy.yml`):

```yaml
name: Deploy to GitHub Pages
on:
  push:
    branches: [ main ]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install and Build
        run: |
          cd src/web/frontend
          npm install
          npm run build
      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: src/web/frontend/dist
```

### 4. AWS S3 + CloudFront

1. **Create S3 bucket** with static website hosting
2. **Upload build files** to bucket
3. **Configure CloudFront** distribution
4. **Set up custom domain** (optional)

### 5. Firebase Hosting

```bash
cd src/web/frontend
npm install -g firebase-tools
firebase login
firebase init hosting
# Select dist as public directory
# Configure as SPA (rewrite all URLs to index.html)
firebase deploy
```

## ⚙️ Configuration Notes

### API Integration

The frontend currently makes API calls to `/api/*` endpoints. For static deployment:

**Option 1: Serverless Functions**
- Netlify Functions (recommended)
- Vercel Functions
- AWS Lambda

**Option 2: External API**
- Deploy the Node.js API separately
- Update API base URL in frontend
- Configure CORS properly

**Option 3: Mock/Demo Mode**
- Implement client-side demo mode
- Use localStorage for persistence
- Show sample diagrams

### Environment Variables

Create `.env.production` in `src/web/frontend/`:
```env
VITE_API_BASE_URL=https://your-api-domain.com
VITE_APP_MODE=production
VITE_ENABLE_ANALYTICS=true
```

### Performance Optimization

The build is already optimized with:
- Code splitting by route and vendor
- Asset optimization and compression
- Tree shaking for unused code
- Modern JS output with legacy fallback

## 🔒 Security

Security headers are configured in:
- `_redirects` (Netlify)
- `vercel.json` (Vercel)
- `netlify.toml` (Netlify)

Headers include:
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

## 📱 Progressive Web App

The app includes PWA features:
- Web App Manifest (`manifest.json`)
- Service Worker ready
- Offline capability (when implemented)
- Install prompt

## 🧪 Testing Deployment

1. **Local testing:**
   ```bash
   npm run build
   npm run preview
   ```

2. **Production testing:**
   - Test all routes work correctly
   - Verify API calls (if applicable)
   - Check mobile responsiveness
   - Test PWA installation

## 🚨 Common Issues

1. **404 on refresh:** Ensure SPA redirects are configured
2. **API calls fail:** Check CORS and API URL configuration
3. **Assets not loading:** Verify base URL and asset paths
4. **Build fails:** Check Node.js version and dependencies

## 📊 Monitoring

Consider adding:
- Google Analytics
- Error tracking (Sentry)
- Performance monitoring
- User feedback tools

## 🔄 CI/CD

All platforms support automatic deployments on git push. Configure webhooks or GitHub Actions for seamless updates.
