/**
 * Enhance Command for ArchitekAI CLI
 * 
 * Advanced diagram enhancement features including watermarks, annotations, and styling.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import path from 'path';
import fs from 'fs-extra';
import { ConfigManager } from '@/core/config/manager';
import { MermaidConverter, ConversionOptions } from '@/core/converter';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner,
  displayError,
  displayInfo,
  displaySuccess,
  formatDuration,
  formatFileSize,
} from '../utils';

const logger = createLogger('EnhanceCommand');

interface EnhanceCommandOptions {
  input?: string;
  output?: string;
  watermark?: string;
  watermarkPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  watermarkOpacity?: number;
  title?: string;
  subtitle?: string;
  author?: string;
  company?: string;
  date?: boolean;
  border?: boolean;
  borderColor?: string;
  borderWidth?: number;
  logo?: string;
  logoPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  format?: 'jpg' | 'png' | 'svg';
  preset?: 'professional' | 'presentation' | 'documentation' | 'social';
}

interface EnhancementPreset {
  watermark: string;
  watermarkPosition: string;
  watermarkOpacity: number;
  border: boolean;
  borderColor: string;
  borderWidth: number;
  title: boolean;
  author: boolean;
  date: boolean;
  description: string;
}

const ENHANCEMENT_PRESETS: Record<string, EnhancementPreset> = {
  professional: {
    watermark: 'CONFIDENTIAL',
    watermarkPosition: 'bottom-right',
    watermarkOpacity: 0.3,
    border: true,
    borderColor: '#2563eb',
    borderWidth: 2,
    title: true,
    author: true,
    date: true,
    description: 'Professional presentation with branding',
  },
  presentation: {
    watermark: '',
    watermarkPosition: 'bottom-right',
    watermarkOpacity: 0.5,
    border: false,
    borderColor: '#000000',
    borderWidth: 1,
    title: true,
    author: false,
    date: false,
    description: 'Clean presentation style',
  },
  documentation: {
    watermark: 'DRAFT',
    watermarkPosition: 'top-right',
    watermarkOpacity: 0.4,
    border: true,
    borderColor: '#6b7280',
    borderWidth: 1,
    title: true,
    author: true,
    date: true,
    description: 'Documentation with metadata',
  },
  social: {
    watermark: '@YourHandle',
    watermarkPosition: 'bottom-left',
    watermarkOpacity: 0.6,
    border: false,
    borderColor: '#000000',
    borderWidth: 0,
    title: false,
    author: false,
    date: false,
    description: 'Social media optimized',
  },
};

export function enhanceCommand(configManager: ConfigManager): Command {
  const command = new Command('enhance');

  command
    .description('Enhance Mermaid diagrams with watermarks, titles, and professional styling')
    .option('-i, --input <path>', 'input .mmd file (without extension)')
    .option('-o, --output <path>', 'output file path')
    .option('--watermark <text>', 'watermark text')
    .option('--watermark-position <position>', 'watermark position', 'bottom-right')
    .option('--watermark-opacity <number>', 'watermark opacity (0-1)', '0.3')
    .option('--title <text>', 'diagram title')
    .option('--subtitle <text>', 'diagram subtitle')
    .option('--author <text>', 'author name')
    .option('--company <text>', 'company name')
    .option('--date', 'add current date')
    .option('--border', 'add border around diagram')
    .option('--border-color <color>', 'border color', '#2563eb')
    .option('--border-width <number>', 'border width in pixels', '2')
    .option('--logo <path>', 'path to logo image')
    .option('--logo-position <position>', 'logo position', 'top-right')
    .option('-f, --format <format>', 'output format (jpg, png, svg)', 'png')
    .option('--preset <preset>', 'enhancement preset (professional, presentation, documentation, social)', 'professional')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architek-ai enhance -i diagram --title "System Architecture"')}
  ${chalk.cyan('architek-ai enhance -i diagram --preset professional --company "Acme Corp"')}
  ${chalk.cyan('architek-ai enhance -i diagram --watermark "CONFIDENTIAL" --border')}
  ${chalk.cyan('architek-ai enhance -i diagram --logo ./logo.png --author "John Doe"')}

Enhancement Presets:
  ${chalk.yellow('professional')} - Corporate branding with borders and metadata
  ${chalk.yellow('presentation')} - Clean style for presentations
  ${chalk.yellow('documentation')} - Technical documentation style
  ${chalk.yellow('social')} - Optimized for social media sharing
    `)
    .action(async (options: EnhanceCommandOptions) => {
      await executeEnhance(options, configManager);
    });

  return command;
}

async function executeEnhance(
  options: EnhanceCommandOptions, 
  configManager: ConfigManager
): Promise<void> {
  const startTime = Date.now();
  let spinner = createSpinner('Initializing enhancement...');
  
  try {
    spinner.start();

    // Validate input
    if (!options.input) {
      spinner.fail('Input file is required');
      displayError('Please specify an input file with -i');
      process.exit(1);
    }

    // Auto-add .mmd extension
    let inputPath = options.input;
    if (!path.extname(inputPath)) {
      inputPath += '.mmd';
    }

    // Resolve path
    if (!path.isAbsolute(inputPath)) {
      inputPath = path.resolve(process.cwd(), inputPath);
    }

    // Check if file exists
    if (!await fs.pathExists(inputPath)) {
      spinner.fail('Input file not found');
      displayError(`File not found: ${inputPath}`);
      process.exit(1);
    }

    // Apply preset
    const preset = ENHANCEMENT_PRESETS[options.preset || 'professional'];
    
    // Read original content
    const originalContent = await fs.readFile(inputPath, 'utf8');
    
    // Generate enhanced content
    spinner.text = 'Generating enhanced diagram...';
    const enhancedContent = await generateEnhancedContent(originalContent, options, preset);
    
    // Create temporary enhanced file
    const tempPath = inputPath.replace('.mmd', '-enhanced.mmd');
    await fs.writeFile(tempPath, enhancedContent);

    // Convert to image
    spinner.text = 'Converting to image...';
    const converter = new MermaidConverter();
    await converter.initialize();

    const outputPath = options.output || inputPath.replace('.mmd', `-enhanced.${options.format || 'png'}`);
    
    const conversionOptions: ConversionOptions = {
      format: options.format || 'png',
      quality: 95,
      width: 3840,
      height: 2160,
      scale: 2,
      theme: 'default',
      backgroundColor: '#ffffff',
      overwrite: true,
    };

    const result = await converter.convertFile(tempPath, conversionOptions);
    
    if (!result.success) {
      throw new Error(result.error || 'Enhancement failed');
    }

    // Move result to final location
    if (result.outputFile !== outputPath) {
      await fs.move(result.outputFile, outputPath, { overwrite: true });
    }

    // Cleanup
    await fs.remove(tempPath);
    await converter.cleanup();

    spinner.stop();

    // Display results
    displaySuccess(`Enhanced diagram created successfully!`);
    displayInfo(`Input: ${inputPath}`);
    displayInfo(`Output: ${outputPath}`);
    displayInfo(`Format: ${(options.format || 'png').toUpperCase()}`);
    displayInfo(`Preset: ${options.preset || 'professional'}`);
    displayInfo(`Size: ${formatFileSize(await fs.stat(outputPath).then(s => s.size))}`);
    displayInfo(`Duration: ${formatDuration(Date.now() - startTime)}`);

  } catch (error) {
    if (spinner) {
      spinner.fail('Enhancement failed');
    }
    
    logger.error('Enhance command failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function generateEnhancedContent(
  originalContent: string,
  options: EnhanceCommandOptions,
  preset: EnhancementPreset
): Promise<string> {
  let enhanced = originalContent;

  // Add title and metadata if specified
  if (options.title || preset.title) {
    const title = options.title || 'Architecture Diagram';
    enhanced = `---
title: ${title}
---\n\n${enhanced}`;
  }

  // Add styling for watermarks and borders
  if (options.watermark || preset.watermark) {
    const watermark = options.watermark || preset.watermark;
    const position = options.watermarkPosition || preset.watermarkPosition;
    const opacity = Number(options.watermarkOpacity) || preset.watermarkOpacity;
    
    // Add watermark styling (this would be implemented in the HTML template)
    enhanced += `\n\n%% Watermark: ${watermark} at ${position} with opacity ${opacity}`;
  }

  if (options.border || preset.border) {
    const borderColor = options.borderColor || preset.borderColor;
    const borderWidth = Number(options.borderWidth) || preset.borderWidth;
    
    enhanced += `\n\n%% Border: ${borderWidth}px ${borderColor}`;
  }

  // Add metadata comments
  if (options.author || preset.author) {
    enhanced += `\n\n%% Author: ${options.author || 'Unknown'}`;
  }

  if (options.company) {
    enhanced += `\n\n%% Company: ${options.company}`;
  }

  if (options.date || preset.date) {
    enhanced += `\n\n%% Generated: ${new Date().toISOString().split('T')[0]}`;
  }

  return enhanced;
}
