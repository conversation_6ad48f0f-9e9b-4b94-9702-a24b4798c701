/**
 * Optimize Command for ArchitekAI CLI
 * 
 * Smart optimization of Mermaid diagrams for better visual output and performance.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import path from 'path';
import fs from 'fs-extra';
import { ConfigManager } from '@/core/config/manager';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner,
  displayError,
  displayInfo,
  displaySuccess,
  displayWarning,
} from '../utils';

const logger = createLogger('OptimizeCommand');

interface OptimizeOptions {
  input?: string;
  output?: string;
  autoLayout?: boolean;
  simplify?: boolean;
  colorOptimize?: boolean;
  accessibilityCheck?: boolean;
  performanceOptimize?: boolean;
  backup?: boolean;
  dryRun?: boolean;
}

interface OptimizationResult {
  original: string;
  optimized: string;
  improvements: string[];
  warnings: string[];
  stats: {
    originalLines: number;
    optimizedLines: number;
    nodesCount: number;
    edgesCount: number;
    complexity: 'low' | 'medium' | 'high';
  };
}

export function optimizeCommand(configManager: ConfigManager): Command {
  const command = new Command('optimize');

  command
    .description('Optimize Mermaid diagrams for better visual output and performance')
    .option('-i, --input <path>', 'input .mmd file (without extension)')
    .option('-o, --output <path>', 'output file path (default: input-optimized.mmd)')
    .option('--auto-layout', 'automatically optimize layout and spacing')
    .option('--simplify', 'simplify complex diagrams by grouping related elements')
    .option('--color-optimize', 'optimize colors for better contrast and accessibility')
    .option('--accessibility-check', 'check and improve accessibility compliance')
    .option('--performance-optimize', 'optimize for faster rendering')
    .option('--backup', 'create backup of original file')
    .option('--dry-run', 'show optimizations without applying them')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architek-ai optimize -i diagram --auto-layout')}
  ${chalk.cyan('architek-ai optimize -i complex-diagram --simplify --color-optimize')}
  ${chalk.cyan('architek-ai optimize -i diagram --accessibility-check --dry-run')}
  ${chalk.cyan('architek-ai optimize -i diagram --performance-optimize --backup')}

Optimization Features:
  • Auto-layout optimization for better visual flow
  • Diagram simplification for complex architectures
  • Color optimization for accessibility
  • Performance improvements for large diagrams
  • Accessibility compliance checking
    `)
    .action(async (options: OptimizeOptions) => {
      await executeOptimize(options, configManager);
    });

  return command;
}

async function executeOptimize(
  options: OptimizeOptions,
  configManager: ConfigManager
): Promise<void> {
  let spinner = createSpinner('Analyzing diagram...');
  
  try {
    spinner.start();

    // Validate input
    if (!options.input) {
      spinner.fail('Input file is required');
      displayError('Please specify an input file with -i');
      process.exit(1);
    }

    // Auto-add .mmd extension
    let inputPath = options.input;
    if (!path.extname(inputPath)) {
      inputPath += '.mmd';
    }

    // Resolve path
    if (!path.isAbsolute(inputPath)) {
      inputPath = path.resolve(process.cwd(), inputPath);
    }

    // Check if file exists
    if (!await fs.pathExists(inputPath)) {
      spinner.fail('Input file not found');
      displayError(`File not found: ${inputPath}`);
      process.exit(1);
    }

    // Read and analyze content
    const originalContent = await fs.readFile(inputPath, 'utf8');
    
    spinner.text = 'Optimizing diagram...';
    const result = await optimizeDiagram(originalContent, options);

    spinner.stop();

    // Display results
    displayOptimizationResults(result);

    if (options.dryRun) {
      displayInfo('\n📋 Dry Run - No files were modified');
      displayInfo('\nOptimized content preview:');
      console.log(chalk.gray('─'.repeat(50)));
      console.log(result.optimized);
      console.log(chalk.gray('─'.repeat(50)));
      return;
    }

    // Create backup if requested
    if (options.backup) {
      const backupPath = inputPath.replace('.mmd', '.backup.mmd');
      await fs.copy(inputPath, backupPath);
      displayInfo(`📄 Backup created: ${backupPath}`);
    }

    // Write optimized content
    const outputPath = options.output || inputPath.replace('.mmd', '-optimized.mmd');
    await fs.writeFile(outputPath, result.optimized);

    displaySuccess(`✅ Optimized diagram saved: ${outputPath}`);

  } catch (error) {
    if (spinner) {
      spinner.fail('Optimization failed');
    }
    
    logger.error('Optimize command failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function optimizeDiagram(content: string, options: OptimizeOptions): Promise<OptimizationResult> {
  const improvements: string[] = [];
  const warnings: string[] = [];
  let optimized = content;

  // Analyze diagram structure
  const stats = analyzeDiagram(content);
  
  // Auto-layout optimization
  if (options.autoLayout) {
    const layoutResult = optimizeLayout(optimized);
    optimized = layoutResult.content;
    improvements.push(...layoutResult.improvements);
  }

  // Simplification
  if (options.simplify && stats.complexity === 'high') {
    const simplifyResult = simplifyDiagram(optimized);
    optimized = simplifyResult.content;
    improvements.push(...simplifyResult.improvements);
  }

  // Color optimization
  if (options.colorOptimize) {
    const colorResult = optimizeColors(optimized);
    optimized = colorResult.content;
    improvements.push(...colorResult.improvements);
  }

  // Accessibility check
  if (options.accessibilityCheck) {
    const accessibilityResult = checkAccessibility(optimized);
    warnings.push(...accessibilityResult.warnings);
    improvements.push(...accessibilityResult.improvements);
  }

  // Performance optimization
  if (options.performanceOptimize) {
    const perfResult = optimizePerformance(optimized);
    optimized = perfResult.content;
    improvements.push(...perfResult.improvements);
  }

  return {
    original: content,
    optimized,
    improvements,
    warnings,
    stats: {
      ...stats,
      optimizedLines: optimized.split('\n').length,
    },
  };
}

function analyzeDiagram(content: string): OptimizationResult['stats'] {
  const lines = content.split('\n');
  const nodes = (content.match(/\w+\[.*?\]/g) || []).length;
  const edges = (content.match(/-->/g) || []).length + (content.match(/---/g) || []).length;
  
  let complexity: 'low' | 'medium' | 'high' = 'low';
  if (nodes > 20 || edges > 30) complexity = 'high';
  else if (nodes > 10 || edges > 15) complexity = 'medium';

  return {
    originalLines: lines.length,
    optimizedLines: lines.length,
    nodesCount: nodes,
    edgesCount: edges,
    complexity,
  };
}

function optimizeLayout(content: string): { content: string; improvements: string[] } {
  const improvements: string[] = [];
  let optimized = content;

  // Add proper spacing
  if (!content.includes('\n\n')) {
    optimized = optimized.replace(/\n(?=\s*\w)/g, '\n\n');
    improvements.push('Added proper spacing between diagram elements');
  }

  // Optimize direction for better flow
  if (content.includes('graph TD') && content.split('\n').length > 15) {
    optimized = optimized.replace('graph TD', 'graph LR');
    improvements.push('Changed to left-right layout for better readability');
  }

  return { content: optimized, improvements };
}

function simplifyDiagram(content: string): { content: string; improvements: string[] } {
  const improvements: string[] = [];
  let optimized = content;

  // Group related services
  const servicePattern = /(\w+Service|\w+API|\w+Controller)/g;
  const services = content.match(servicePattern) || [];
  
  if (services.length > 5) {
    improvements.push(`Identified ${services.length} services that could be grouped`);
    improvements.push('Consider using subgraphs to group related services');
  }

  return { content: optimized, improvements };
}

function optimizeColors(content: string): { content: string; improvements: string[] } {
  const improvements: string[] = [];
  let optimized = content;

  // Add color classes for better visual hierarchy
  if (!content.includes('classDef')) {
    optimized += '\n\n%% Color optimization\n';
    optimized += 'classDef primary fill:#e1f5fe,stroke:#01579b,stroke-width:2px\n';
    optimized += 'classDef secondary fill:#f3e5f5,stroke:#4a148c,stroke-width:2px\n';
    optimized += 'classDef database fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px\n';
    
    improvements.push('Added color classes for better visual hierarchy');
    improvements.push('Applied accessibility-friendly color palette');
  }

  return { content: optimized, improvements };
}

function checkAccessibility(content: string): { warnings: string[]; improvements: string[] } {
  const warnings: string[] = [];
  const improvements: string[] = [];

  // Check for proper labeling
  const unlabeledNodes = (content.match(/\w+\[\]/g) || []).length;
  if (unlabeledNodes > 0) {
    warnings.push(`Found ${unlabeledNodes} unlabeled nodes - add descriptive labels`);
  }

  // Check for color-only information
  if (content.includes('fill:') && !content.includes('stroke:')) {
    warnings.push('Using color-only differentiation - add patterns or shapes');
  }

  // Suggest improvements
  if (!content.includes('title:')) {
    improvements.push('Add a title for better context');
  }

  return { warnings, improvements };
}

function optimizePerformance(content: string): { content: string; improvements: string[] } {
  const improvements: string[] = [];
  let optimized = content;

  // Remove redundant whitespace
  optimized = optimized.replace(/\n\s*\n\s*\n/g, '\n\n');
  improvements.push('Removed redundant whitespace');

  // Optimize node definitions
  const nodeCount = (content.match(/\w+\[.*?\]/g) || []).length;
  if (nodeCount > 50) {
    improvements.push('Large diagram detected - consider splitting into multiple diagrams');
  }

  return { content: optimized, improvements };
}

function displayOptimizationResults(result: OptimizationResult): void {
  displayInfo('\n📊 Optimization Results:');
  displayInfo(`   Original Lines: ${result.stats.originalLines}`);
  displayInfo(`   Optimized Lines: ${result.stats.optimizedLines}`);
  displayInfo(`   Nodes: ${result.stats.nodesCount}`);
  displayInfo(`   Edges: ${result.stats.edgesCount}`);
  displayInfo(`   Complexity: ${result.stats.complexity.toUpperCase()}`);

  if (result.improvements.length > 0) {
    displayInfo('\n✅ Improvements Applied:');
    result.improvements.forEach(improvement => {
      displaySuccess(`   • ${improvement}`);
    });
  }

  if (result.warnings.length > 0) {
    displayInfo('\n⚠️  Recommendations:');
    result.warnings.forEach(warning => {
      displayWarning(`   • ${warning}`);
    });
  }
}
