/**
 * Mermaid Converter for ArchitekAI
 * 
 * <PERSON>les conversion of Mermaid (.mmd) files to various image formats
 * using Puppeteer for headless browser rendering.
 */

import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import sharp from 'sharp';
import fs from 'fs-extra';
import path from 'path';
import { createLogger } from '@/utils/logger';

const logger = createLogger('MermaidConverter');

export interface ConversionOptions {
  format: 'jpg' | 'jpeg' | 'png' | 'svg' | 'pdf';
  quality?: number; // 1-100 for JPEG
  width?: number;
  height?: number;
  scale?: number; // Device scale factor
  backgroundColor?: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  outputDir?: string;
  overwrite?: boolean;
  autoSize?: boolean; // Automatically size based on content
  padding?: number; // Padding around the diagram
  cropToContent?: boolean; // Crop to actual content bounds
}

export interface ConversionResult {
  inputFile: string;
  outputFile: string;
  format: string;
  size: number;
  width: number;
  height: number;
  success: boolean;
  error?: string;
  duration: number;
}

export interface BatchConversionResult {
  results: ConversionResult[];
  totalProcessed: number;
  successful: number;
  failed: number;
  totalDuration: number;
  errors: Array<{ file: string; error: string }>;
}

export class MermaidConverter {
  private browser: Browser | null = null;
  private isInitialized = false;

  constructor() {}

  /**
   * Initialize the converter with Puppeteer browser instance
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Initializing Mermaid converter...');
      
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--force-device-scale-factor=2',
          '--high-dpi-support=1',
          '--force-color-profile=srgb'
        ],
        defaultViewport: null
      });

      this.isInitialized = true;
      logger.info('Mermaid converter initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Mermaid converter:', error);
      throw new Error(`Converter initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.isInitialized = false;
      logger.info('Mermaid converter cleaned up');
    }
  }

  /**
   * Convert a single Mermaid file to the specified format
   */
  async convertFile(inputPath: string, options: ConversionOptions): Promise<ConversionResult> {
    const startTime = Date.now();
    
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Validate input file
      if (!await fs.pathExists(inputPath)) {
        throw new Error(`Input file not found: ${inputPath}`);
      }

      const inputContent = await fs.readFile(inputPath, 'utf8');
      if (!inputContent.trim()) {
        throw new Error('Input file is empty');
      }

      // Generate output path
      const outputPath = this.generateOutputPath(inputPath, options);
      
      // Check if output file exists and handle overwrite
      if (!options.overwrite && await fs.pathExists(outputPath)) {
        throw new Error(`Output file already exists: ${outputPath}`);
      }

      // Ensure output directory exists
      await fs.ensureDir(path.dirname(outputPath));

      // Convert the diagram
      const { buffer, width, height } = await this.renderMermaidDiagram(inputContent, options);
      
      // Save the result
      await fs.writeFile(outputPath, buffer);
      const stats = await fs.stat(outputPath);

      const duration = Date.now() - startTime;

      return {
        inputFile: inputPath,
        outputFile: outputPath,
        format: options.format,
        size: stats.size,
        width,
        height,
        success: true,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logger.error(`Conversion failed for ${inputPath}:`, error);
      
      return {
        inputFile: inputPath,
        outputFile: '',
        format: options.format,
        size: 0,
        width: 0,
        height: 0,
        success: false,
        error: errorMessage,
        duration
      };
    }
  }

  /**
   * Convert multiple Mermaid files in batch
   */
  async convertBatch(inputPaths: string[], options: ConversionOptions): Promise<BatchConversionResult> {
    const startTime = Date.now();
    const results: ConversionResult[] = [];
    const errors: Array<{ file: string; error: string }> = [];

    logger.info(`Starting batch conversion of ${inputPaths.length} files`);

    for (const inputPath of inputPaths) {
      try {
        const result = await this.convertFile(inputPath, options);
        results.push(result);
        
        if (!result.success && result.error) {
          errors.push({ file: inputPath, error: result.error });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push({ file: inputPath, error: errorMessage });
        
        // Add failed result
        results.push({
          inputFile: inputPath,
          outputFile: '',
          format: options.format,
          size: 0,
          width: 0,
          height: 0,
          success: false,
          error: errorMessage,
          duration: 0
        });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;
    const totalDuration = Date.now() - startTime;

    logger.info(`Batch conversion completed: ${successful} successful, ${failed} failed`);

    return {
      results,
      totalProcessed: results.length,
      successful,
      failed,
      totalDuration,
      errors
    };
  }

  /**
   * Render Mermaid diagram using Puppeteer
   */
  private async renderMermaidDiagram(
    mermaidContent: string,
    options: ConversionOptions
  ): Promise<{ buffer: Buffer; width: number; height: number }> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();

    try {
      // Set high-resolution viewport for better quality
      const targetWidth = Number(options.width) || 1920;
      const targetHeight = Number(options.height) || 1080;
      const scale = Number(options.scale) || 2; // Default to 2x for high-DPI

      // Use a larger viewport for better rendering quality
      const viewportWidth = Math.max(targetWidth, 1920);
      const viewportHeight = Math.max(targetHeight, 1080);

      await page.setViewport({
        width: viewportWidth,
        height: viewportHeight,
        deviceScaleFactor: scale
      });

      // Create HTML content with Mermaid
      const htmlContent = this.createMermaidHTML(mermaidContent, options);
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

      // Wait for Mermaid to render
      await page.waitForSelector('#mermaid-diagram svg', { timeout: 30000 });

      // Get the SVG element and its dimensions
      const svgElement = await page.$('#mermaid-diagram svg');
      if (!svgElement) {
        throw new Error('Failed to find rendered Mermaid diagram');
      }

      // Get natural SVG dimensions
      const boundingBox = await svgElement.boundingBox();
      if (!boundingBox) {
        throw new Error('Failed to get diagram dimensions');
      }

      let buffer: Buffer;
      let finalWidth = targetWidth;
      let finalHeight = targetHeight;

      if (options.format === 'svg') {
        // For SVG, get the SVG content directly and scale it
        const svgContent = await page.evaluate((width, height) => {
          const svg = (globalThis as any).document.querySelector('#mermaid-diagram svg');
          if (svg) {
            // Set explicit dimensions on SVG for better scaling
            svg.setAttribute('width', width.toString());
            svg.setAttribute('height', height.toString());
            svg.setAttribute('viewBox', `0 0 ${svg.getBBox().width} ${svg.getBBox().height}`);
            return svg.outerHTML;
          }
          return '';
        }, targetWidth, targetHeight);

        buffer = Buffer.from(svgContent, 'utf8');
        finalWidth = targetWidth;
        finalHeight = targetHeight;
      } else {
        // For raster formats, take a full-page screenshot for maximum quality
        const screenshotOptions: any = {
          type: options.format === 'jpg' || options.format === 'jpeg' ? 'jpeg' : 'png',
          fullPage: false,
          omitBackground: options.backgroundColor === 'transparent',
          clip: {
            x: 0,
            y: 0,
            width: viewportWidth,
            height: viewportHeight
          }
        };

        if (options.format === 'jpg' || options.format === 'jpeg') {
          screenshotOptions.quality = Math.min(100, Math.max(1, Number(options.quality) || 95));
        }

        const screenshotBuffer = await page.screenshot(screenshotOptions);

        // Use Sharp to resize and optimize the image
        let sharpInstance = sharp(screenshotBuffer);

        // Get image metadata to determine current dimensions
        const metadata = await sharpInstance.metadata();
        const currentWidth = metadata.width || viewportWidth;
        const currentHeight = metadata.height || viewportHeight;

        // Calculate scaling to fit target dimensions while maintaining aspect ratio
        const scaleX = targetWidth / currentWidth;
        const scaleY = targetHeight / currentHeight;
        const scaleFactor = Math.min(scaleX, scaleY);

        const scaledWidth = Math.round(currentWidth * scaleFactor);
        const scaledHeight = Math.round(currentHeight * scaleFactor);

        // Resize with high-quality settings
        sharpInstance = sharpInstance.resize(scaledWidth, scaledHeight, {
          kernel: sharp.kernel.lanczos3,
          fit: 'inside',
          withoutEnlargement: false
        });

        // Apply background color if specified
        if (options.backgroundColor && options.backgroundColor !== 'transparent') {
          sharpInstance = sharpInstance.flatten({ background: options.backgroundColor });
        }

        // Apply format-specific optimizations
        if (options.format === 'jpg' || options.format === 'jpeg') {
          buffer = await sharpInstance
            .jpeg({
              quality: Math.min(100, Math.max(1, Number(options.quality) || 95)),
              progressive: true,
              mozjpeg: true
            })
            .toBuffer();
        } else if (options.format === 'png') {
          buffer = await sharpInstance
            .png({
              compressionLevel: 6,
              progressive: true,
              quality: Math.min(100, Math.max(1, Number(options.quality) || 95))
            })
            .toBuffer();
        } else {
          buffer = await sharpInstance.toBuffer();
        }

        finalWidth = scaledWidth;
        finalHeight = scaledHeight;
      }

      return {
        buffer,
        width: finalWidth,
        height: finalHeight
      };

    } finally {
      await page.close();
    }
  }

  /**
   * Create HTML content for Mermaid rendering
   */
  private createMermaidHTML(mermaidContent: string, options: ConversionOptions): string {
    const theme = options.theme || 'default';
    const backgroundColor = options.backgroundColor || (theme === 'dark' ? '#1e1e1e' : '#ffffff');
    const width = Number(options.width) || 1920;
    const height = Number(options.height) || 1080;

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Diagram</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            margin: 0;
            padding: 40px;
            background-color: ${backgroundColor};
            font-family: 'Segoe UI', 'Arial', sans-serif;
            width: ${width}px;
            height: ${height}px;
            overflow: hidden;
        }
        #mermaid-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            min-width: ${width - 80}px;
            min-height: ${height - 80}px;
        }
        .mermaid {
            max-width: 100%;
            max-height: 100%;
        }
        /* High-DPI optimizations */
        svg {
            shape-rendering: geometricPrecision;
            text-rendering: optimizeLegibility;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
        }
    </style>
</head>
<body>
    <div id="mermaid-diagram">
        <div class="mermaid">
${mermaidContent}
        </div>
    </div>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: '${theme}',
            themeVariables: {
                primaryColor: '#ff6b6b',
                primaryTextColor: '#333',
                primaryBorderColor: '#ff6b6b',
                lineColor: '#333',
                secondaryColor: '#4ecdc4',
                tertiaryColor: '#ffe66d'
            },
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: false
            },
            gantt: {
                useMaxWidth: false
            },
            journey: {
                useMaxWidth: false
            },
            timeline: {
                useMaxWidth: false
            }
        });

        // Wait for diagram to render, then optimize SVG
        window.addEventListener('load', function() {
            setTimeout(function() {
                const svg = document.querySelector('#mermaid-diagram svg');
                if (svg) {
                    // Ensure SVG has proper dimensions
                    const bbox = svg.getBBox();
                    svg.setAttribute('width', '${width - 80}');
                    svg.setAttribute('height', '${height - 80}');
                    svg.setAttribute('viewBox', \`0 0 \${bbox.width} \${bbox.height}\`);
                    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');
                }
            }, 100);
        });
    </script>
</body>
</html>`;
  }

  /**
   * Generate output file path based on input and options
   */
  private generateOutputPath(inputPath: string, options: ConversionOptions): string {
    const inputDir = path.dirname(inputPath);
    const inputName = path.basename(inputPath, path.extname(inputPath));
    const outputDir = options.outputDir || inputDir;
    const extension = options.format === 'jpg' ? 'jpg' : options.format;
    
    return path.join(outputDir, `${inputName}.${extension}`);
  }
}
