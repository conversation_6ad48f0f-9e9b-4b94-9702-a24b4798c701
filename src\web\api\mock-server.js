/**
 * Mock API Server for ArchitekAI Web Frontend Testing
 * This is a simple Express server that provides mock endpoints for testing the frontend
 */

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configure multer for file uploads
const upload = multer({ dest: 'uploads/' });

// Mock data
const mockTemplates = [
  {
    id: 'microservices',
    name: 'Microservices Architecture',
    description: 'Modern microservices architecture with API gateway, service discovery, and distributed data management',
    complexity: 'Moderate',
    components: 12,
    pattern: 'microservices'
  },
  {
    id: 'monolithic',
    name: 'Monolithic Architecture', 
    description: 'Traditional monolithic architecture with layered design and centralized data management',
    complexity: 'Simple',
    components: 5,
    pattern: 'monolithic'
  },
  {
    id: 'serverless',
    name: 'Serverless Architecture',
    description: 'Event-driven serverless architecture with functions, queues, and managed services',
    complexity: 'Moderate',
    components: 10,
    pattern: 'serverless'
  }
];

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Generate diagram endpoint
app.post('/api/generate', (req, res) => {
  const { description, format = 'mermaid', template } = req.body;
  
  console.log('Generate request:', { description, format, template });
  
  // Mock diagram generation based on description
  let mockDiagram = '';
  
  if (description.toLowerCase().includes('microservices') || template === 'microservices') {
    mockDiagram = `graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Order Service]
    A --> D[Payment Service]
    A --> E[Notification Service]
    B --> F[(User DB)]
    C --> G[(Order DB)]
    D --> H[(Payment DB)]
    C --> I[Message Queue]
    I --> E
    J[Service Discovery] --> B
    J --> C
    J --> D
    J --> E`;
  } else if (description.toLowerCase().includes('serverless') || template === 'serverless') {
    mockDiagram = `graph TD
    A[CloudFront CDN] --> B[API Gateway]
    B --> C[Auth Function]
    B --> D[User Function]
    B --> E[Data Function]
    C --> F[(DynamoDB)]
    D --> F
    E --> F
    E --> G[S3 Bucket]
    H[EventBridge] --> I[Processing Function]
    I --> J[SQS Queue]
    J --> K[Worker Function]`;
  } else if (description.toLowerCase().includes('monolithic') || template === 'monolithic') {
    mockDiagram = `graph TD
    A[Load Balancer] --> B[Web Server]
    B --> C[Application Server]
    C --> D[Business Logic Layer]
    D --> E[Data Access Layer]
    E --> F[(Database)]
    C --> G[Cache Layer]`;
  } else {
    // Generic web application
    mockDiagram = `graph TD
    A[User] --> B[Web Browser]
    B --> C[Load Balancer]
    C --> D[Web Server]
    D --> E[Application Server]
    E --> F[(Database)]
    E --> G[Cache]
    D --> H[File Storage]`;
  }
  
  // Simulate processing time
  setTimeout(() => {
    res.json({
      success: true,
      content: mockDiagram,
      format: format,
      metadata: {
        description: description,
        template: template,
        generatedAt: new Date().toISOString(),
        components: mockDiagram.split('\n').length - 1
      }
    });
  }, 1000 + Math.random() * 2000); // 1-3 second delay
});

// Convert diagram endpoint (mock image conversion)
app.post('/api/convert', (req, res) => {
  const { content, format = 'jpg', options = {} } = req.body;
  
  console.log('Convert request:', { format, options });
  
  // Mock image conversion - return a simple colored rectangle as a placeholder
  const width = options.width || 1920;
  const height = options.height || 1080;
  const quality = options.quality || 90;
  
  // Create a simple SVG as mock output
  const mockSvg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="#f0f9ff"/>
    <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="24" fill="#1e40af">
      Mock Diagram Export (${format.toUpperCase()})
    </text>
    <text x="50%" y="60%" text-anchor="middle" font-family="Arial" font-size="16" fill="#64748b">
      ${width}x${height} - Quality: ${quality}%
    </text>
  </svg>`;
  
  if (format === 'svg') {
    res.setHeader('Content-Type', 'image/svg+xml');
    res.setHeader('Content-Disposition', 'attachment; filename="diagram.svg"');
    res.send(mockSvg);
  } else {
    // For JPG/PNG, we'll return the SVG but with appropriate headers
    // In a real implementation, this would be converted to the actual format
    res.setHeader('Content-Type', format === 'jpg' ? 'image/jpeg' : 'image/png');
    res.setHeader('Content-Disposition', `attachment; filename="diagram.${format}"`);
    res.send(Buffer.from(mockSvg));
  }
});

// Templates endpoint
app.get('/api/templates', (req, res) => {
  res.json({
    success: true,
    templates: mockTemplates
  });
});

// Template detail endpoint
app.get('/api/templates/:id', (req, res) => {
  const template = mockTemplates.find(t => t.id === req.params.id);
  if (template) {
    res.json({ success: true, template });
  } else {
    res.status(404).json({ success: false, error: 'Template not found' });
  }
});

// File upload endpoint
app.post('/api/upload', upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ success: false, error: 'No file uploaded' });
  }
  
  // Mock file processing
  res.json({
    success: true,
    filename: req.file.originalname,
    size: req.file.size,
    content: `graph TD
    A[Uploaded File] --> B[Processing]
    B --> C[Mock Content]
    C --> D[Ready for Preview]`
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.path
  });
});

// Start server
app.listen(port, () => {
  console.log(`🚀 Mock API Server running at http://localhost:${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`🔧 Available endpoints:`);
  console.log(`   POST /api/generate - Generate diagrams`);
  console.log(`   POST /api/convert - Convert diagrams to images`);
  console.log(`   GET  /api/templates - List templates`);
  console.log(`   POST /api/upload - Upload files`);
});
