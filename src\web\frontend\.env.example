# Environment Configuration Template
# Copy this file to .env.local and configure for your environment

# Application Mode
VITE_APP_MODE=development

# API Configuration
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=30000

# Application Settings
VITE_APP_VERSION=1.0.0
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# External Services (Optional)
VITE_SENTRY_DSN=your_sentry_dsn_here
VITE_GA_TRACKING_ID=your_google_analytics_id_here

# Feature Flags
VITE_ENABLE_LIVE_PREVIEW=true
VITE_ENABLE_EXPORT=true
VITE_ENABLE_TEMPLATES=true
VITE_ENABLE_GALLERY=true

# File Upload Settings
VITE_MAX_FILE_SIZE=10485760
VITE_SUPPORTED_FORMATS=mermaid,plantuml,ascii,drawio

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_PWA=true
