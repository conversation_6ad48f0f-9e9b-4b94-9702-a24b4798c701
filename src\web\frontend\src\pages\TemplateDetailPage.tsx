import React, { useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { MermaidPreview } from '../components/MermaidPreview';
import { ExportButton } from '../components/ExportButton';
import { useTheme } from '../hooks/useTheme';

const TemplateDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { resolvedTheme } = useTheme();
  const [showPreview, setShowPreview] = useState(false);

  // Mock template data - in real app, this would come from API
  const getTemplateData = (templateId: string) => {
    const templates = {
      microservices: {
        id: 'microservices',
        name: 'Microservices Architecture',
        description: 'Modern microservices architecture with API gateway, service discovery, and distributed data management',
        complexity: 'Moderate',
        components: 12,
        pattern: 'microservices',
        useCases: [
          'Scalable web applications',
          'Distributed systems',
          'Cloud-native applications',
          'Enterprise applications'
        ],
        prerequisites: [
          'Containerization knowledge',
          'API design experience',
          'Distributed systems understanding'
        ],
        sampleDiagram: `graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Order Service]
    A --> D[Payment Service]
    A --> E[Notification Service]
    B --> F[(User DB)]
    C --> G[(Order DB)]
    D --> H[(Payment DB)]
    C --> I[Message Queue]
    I --> E
    J[Service Discovery] --> B
    J --> C
    J --> D
    J --> E`
      },
      monolithic: {
        id: 'monolithic',
        name: 'Monolithic Architecture',
        description: 'Traditional monolithic architecture with layered design and centralized data management',
        complexity: 'Simple',
        components: 5,
        pattern: 'monolithic',
        useCases: [
          'Small to medium applications',
          'Rapid prototyping',
          'Simple business logic',
          'Team learning projects'
        ],
        prerequisites: [
          'Basic web development',
          'Database design',
          'MVC pattern understanding'
        ],
        sampleDiagram: `graph TD
    A[Load Balancer] --> B[Web Server]
    B --> C[Application Server]
    C --> D[Business Logic Layer]
    D --> E[Data Access Layer]
    E --> F[(Database)]
    C --> G[Cache Layer]`
      },
      serverless: {
        id: 'serverless',
        name: 'Serverless Architecture',
        description: 'Event-driven serverless architecture with functions, queues, and managed services',
        complexity: 'Moderate',
        components: 10,
        pattern: 'serverless',
        useCases: [
          'Event-driven applications',
          'Auto-scaling workloads',
          'Cost-optimized solutions',
          'Rapid development cycles'
        ],
        prerequisites: [
          'Cloud platform knowledge',
          'Event-driven patterns',
          'Function-as-a-Service understanding'
        ],
        sampleDiagram: `graph TD
    A[CloudFront CDN] --> B[API Gateway]
    B --> C[Auth Function]
    B --> D[User Function]
    B --> E[Data Function]
    C --> F[(DynamoDB)]
    D --> F
    E --> F
    E --> G[S3 Bucket]
    H[EventBridge] --> I[Processing Function]
    I --> J[SQS Queue]
    J --> K[Worker Function]`
      }
    };

    return templates[templateId as keyof typeof templates] || templates.microservices;
  };

  const template = getTemplateData(id || 'microservices');

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <Link to="/templates" className="text-blue-600 hover:text-blue-700 text-sm">
              ← Back to Templates
            </Link>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{template.name}</h1>
            <p className="text-gray-600 mb-6">{template.description}</p>
            
            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-lg font-semibold mb-3">Template Details</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Pattern:</span>
                    <span className="font-medium">{template.pattern}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Complexity:</span>
                    <span className="font-medium">{template.complexity}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Components:</span>
                    <span className="font-medium">{template.components}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-3">Use Cases</h3>
                <ul className="space-y-1">
                  {template.useCases.map((useCase, index) => (
                    <li key={index} className="text-gray-600 text-sm">• {useCase}</li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-3">Prerequisites</h3>
              <ul className="space-y-1">
                {template.prerequisites.map((prereq, index) => (
                  <li key={index} className="text-gray-600 text-sm">• {prereq}</li>
                ))}
              </ul>
            </div>
            
            {/* Preview Section */}
            {showPreview && template.sampleDiagram && (
              <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Template Preview</h3>
                  <ExportButton
                    content={template.sampleDiagram}
                    filename={`template-${template.id}`}
                    className="scale-90"
                  />
                </div>
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                  <MermaidPreview
                    content={template.sampleDiagram}
                    theme={resolvedTheme === 'dark' ? 'dark' : 'default'}
                    className="min-h-[300px]"
                  />
                </div>
                <div className="mt-4">
                  <details>
                    <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                      View Mermaid Code
                    </summary>
                    <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono">
                      <pre className="whitespace-pre-wrap">{template.sampleDiagram}</pre>
                    </div>
                  </details>
                </div>
              </div>
            )}

            <div className="flex flex-wrap gap-4">
              <Link
                to={`/generate?template=${template.id}`}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Use This Template
              </Link>
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                {showPreview ? 'Hide Preview' : 'Preview Diagram'}
              </button>
              {template.sampleDiagram && (
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(template.sampleDiagram);
                    // You could add a toast notification here
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  Copy Sample Code
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateDetailPage;
